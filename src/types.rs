use napi::bindgen_prelude::*;
use napi_derive::napi;

/// JavaScript-compatible Entry structure
#[napi(object)]
pub struct JsEntry {
    /// Slot number from Jito Shredstream
    pub slot: u64,
    /// Number of hashes since the previous Entry ID
    pub num_hashes: u64,
    /// SHA-256 hash (32 bytes)
    pub hash: Buffer,
    /// Array of transactions in this entry
    pub transactions: Vec<JsTransaction>,
}

/// JavaScript-compatible Transaction structure
#[napi(object)]
pub struct JsTransaction {
    /// Array of 64-byte signatures
    pub signatures: Vec<Buffer>,
    /// Transaction message (Legacy or V0)
    pub message: JsVersionedMessage,
}

/// JavaScript-compatible VersionedMessage structure
#[napi(object)]
pub struct JsVersionedMessage {
    /// Message version: 0 = Legacy, 1 = V0
    pub version: u32,
    /// Message header
    pub header: JsMessageHeader,
    /// Array of 32-byte account keys
    pub account_keys: Vec<Buffer>,
    /// 32-byte recent blockhash
    pub recent_blockhash: Buffer,
    /// Array of compiled instructions
    pub instructions: Vec<JsCompiledInstruction>,
    /// Address table lookups (only for V0 messages)
    pub address_table_lookups: Option<Vec<JsAddressTableLookup>>,
}

/// JavaScript-compatible MessageHeader structure
#[napi(object)]
pub struct JsMessageHeader {
    /// Number of required signatures
    pub num_required_signatures: u8,
    /// Number of readonly signed accounts
    pub num_readonly_signed_accounts: u8,
    /// Number of readonly unsigned accounts
    pub num_readonly_unsigned_accounts: u8,
}

/// JavaScript-compatible CompiledInstruction structure
#[napi(object)]
pub struct JsCompiledInstruction {
    /// Index into the message's account keys array
    pub program_id_index: u8,
    /// Ordered indices into the message's account keys array
    pub accounts: Vec<u8>,
    /// Instruction data
    pub data: Buffer,
}

/// JavaScript-compatible AddressTableLookup structure (V0 only)
#[napi(object)]
pub struct JsAddressTableLookup {
    /// 32-byte address table account key
    pub account_key: Buffer,
    /// Indices of writable accounts to load
    pub writable_indexes: Vec<u8>,
    /// Indices of readonly accounts to load
    pub readonly_indexes: Vec<u8>,
}

/// Error types for better debugging
#[derive(Debug, thiserror::Error)]
pub enum DecodeError {
    #[error("Failed to deserialize entries: {0}")]
    DeserializationFailed(String),
    #[error("Failed to convert Solana types to JavaScript types: {0}")]
    ConversionFailed(String),
    #[error("Invalid data format: {0}")]
    InvalidData(String),
    #[error("Unsupported transaction version: {0}")]
    UnsupportedVersion(u8),
}

impl From<DecodeError> for napi::Error {
    fn from(err: DecodeError) -> Self {
        napi::Error::from_reason(format!("JitoDecodeError: {}", err))
    }
}

impl From<bincode::Error> for DecodeError {
    fn from(err: bincode::Error) -> Self {
        DecodeError::DeserializationFailed(err.to_string())
    }
}
