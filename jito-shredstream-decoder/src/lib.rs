#![deny(clippy::all)]

use napi::bindgen_prelude::*;
use napi_derive::napi;
use solana_entry::entry::Entry;

mod converter;
mod types;

pub use converter::*;
pub use types::*;

/// Main decode function for Jito Shredstream entries
///
/// # Arguments
/// * `slot` - Slot number from Jito Shredstream
/// * `data` - Serialized Vec<Entry> bytes from Jito Shredstream
///
/// # Returns
/// Array of decoded entries with structured transaction data
///
/// # Example
/// ```javascript
/// import { decodeEntries } from 'jito-shredstream-decoder';
///
/// const jitoEntry = {
///     slot: 12345,
///     entries: Buffer.from([/* serialized data */])
/// };
///
/// const decodedEntries = decodeEntries(jitoEntry.slot, jitoEntry.entries);
/// console.log(`Decoded ${decodedEntries.length} entries`);
/// ```
#[napi]
pub fn decode_entries(slot: BigInt, data: Buffer) -> Result<Vec<JsEntry>> {
  // Use official Solana deserialization - 100% accurate!
  let entries: Vec<Entry> = bincode::deserialize(&data)
    .map_err(|e| napi::Error::from_reason(format!("Failed to deserialize entries: {}", e)))?;

  // Convert to JavaScript-compatible format
  let js_entries = entries
    .into_iter()
    .map(|entry| convert_entry_to_js(slot, entry))
    .collect::<Result<Vec<_>, _>>()
    .map_err(|e| napi::Error::from_reason(format!("Failed to convert entries: {}", e)))?;

  Ok(js_entries)
}

/// Get library version and build information
#[napi]
pub fn get_version_info() -> String {
  format!(
    "jito-shredstream-decoder v{} (built with Rust using Solana crates)",
    env!("CARGO_PKG_VERSION")
  )
}
