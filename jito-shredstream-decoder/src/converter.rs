use crate::types::*;
use napi::bindgen_prelude::*;
use solana_entry::entry::Entry;
use solana_message::compiled_instruction::CompiledInstruction;
use solana_message::{MessageHeader, VersionedMessage, v0::MessageAddressTableLookup};
use solana_transaction::versioned::VersionedTransaction;

/// Convert Solana Entry to JavaScript-compatible Entry
pub fn convert_entry_to_js(slot: BigInt, entry: Entry) -> Result<JsEntry, String> {
  let transactions = entry
    .transactions
    .into_iter()
    .map(convert_transaction_to_js)
    .collect::<Result<Vec<_>, _>>()?;

  Ok(JsEntry {
    slot,
    num_hashes: entry.num_hashes.into(),
    hash: entry.hash.to_bytes().to_vec().into(),
    transactions,
  })
}

/// Convert Solana VersionedTransaction to JavaScript-compatible Transaction
pub fn convert_transaction_to_js(tx: VersionedTransaction) -> Result<JsTransaction, String> {
  let signatures = tx
    .signatures
    .into_iter()
    .map(|sig| sig.as_ref().to_vec().into())
    .collect();

  let message = convert_versioned_message_to_js(tx.message)?;

  Ok(JsTransaction {
    signatures,
    message,
  })
}

/// Convert Solana VersionedMessage to JavaScript-compatible VersionedMessage
pub fn convert_versioned_message_to_js(
  message: VersionedMessage,
) -> Result<JsVersionedMessage, String> {
  match message {
    VersionedMessage::Legacy(legacy_msg) => {
      let js_message = JsVersionedMessage {
        version: 0,
        header: convert_header_to_js(legacy_msg.header),
        account_keys: legacy_msg
          .account_keys
          .into_iter()
          .map(|key| key.to_bytes().to_vec().into())
          .collect(),
        recent_blockhash: legacy_msg.recent_blockhash.to_bytes().to_vec().into(),
        instructions: legacy_msg
          .instructions
          .into_iter()
          .map(convert_instruction_to_js)
          .collect(),
        address_table_lookups: None,
      };
      Ok(js_message)
    }
    VersionedMessage::V0(v0_msg) => {
      let js_message = JsVersionedMessage {
        version: 1,
        header: convert_header_to_js(v0_msg.header),
        account_keys: v0_msg
          .account_keys
          .into_iter()
          .map(|key| key.to_bytes().to_vec().into())
          .collect(),
        recent_blockhash: v0_msg.recent_blockhash.to_bytes().to_vec().into(),
        instructions: v0_msg
          .instructions
          .into_iter()
          .map(convert_instruction_to_js)
          .collect(),
        address_table_lookups: Some(
          v0_msg
            .address_table_lookups
            .into_iter()
            .map(convert_address_table_lookup_to_js)
            .collect(),
        ),
      };
      Ok(js_message)
    }
  }
}

/// Convert Solana MessageHeader to JavaScript-compatible MessageHeader
pub fn convert_header_to_js(header: MessageHeader) -> JsMessageHeader {
  JsMessageHeader {
    num_required_signatures: header.num_required_signatures,
    num_readonly_signed_accounts: header.num_readonly_signed_accounts,
    num_readonly_unsigned_accounts: header.num_readonly_unsigned_accounts,
  }
}

/// Convert Solana CompiledInstruction to JavaScript-compatible CompiledInstruction
pub fn convert_instruction_to_js(instruction: CompiledInstruction) -> JsCompiledInstruction {
  JsCompiledInstruction {
    program_id_index: instruction.program_id_index,
    accounts: instruction.accounts,
    data: instruction.data.into(),
  }
}

/// Convert Solana MessageAddressTableLookup to JavaScript-compatible AddressTableLookup
pub fn convert_address_table_lookup_to_js(
  lookup: MessageAddressTableLookup,
) -> JsAddressTableLookup {
  JsAddressTableLookup {
    account_key: lookup.account_key.to_bytes().to_vec().into(),
    writable_indexes: lookup.writable_indexes,
    readonly_indexes: lookup.readonly_indexes,
  }
}

#[cfg(test)]
mod tests {
  use super::*;
  use solana_hash::Hash;
  use solana_pubkey::Pubkey;

  #[test]
  fn test_convert_entry_to_js() {
    let entry = Entry {
      num_hashes: 42,
      hash: Hash::new_from_array([1u8; 32]),
      transactions: vec![],
    };

    let js_entry = convert_entry_to_js(12345, entry).unwrap();

    // Note: In actual usage, these would be BigInt values in JavaScript
    // For testing, we just verify the conversion worked without panicking
    assert_eq!(js_entry.hash.len(), 32);
    assert_eq!(js_entry.transactions.len(), 0);
  }

  #[test]
  fn test_convert_header_to_js() {
    let header = MessageHeader {
      num_required_signatures: 1,
      num_readonly_signed_accounts: 0,
      num_readonly_unsigned_accounts: 2,
    };

    let js_header = convert_header_to_js(header);

    assert_eq!(js_header.num_required_signatures, 1);
    assert_eq!(js_header.num_readonly_signed_accounts, 0);
    assert_eq!(js_header.num_readonly_unsigned_accounts, 2);
  }
}
