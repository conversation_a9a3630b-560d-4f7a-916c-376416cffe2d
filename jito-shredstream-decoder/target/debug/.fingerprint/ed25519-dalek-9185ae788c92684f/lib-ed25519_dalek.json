{"rustc": 15497389221046826682, "features": "[\"default\", \"rand\", \"serde_crate\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"asm\", \"batch\", \"batch_deterministic\", \"default\", \"legacy_compatibility\", \"merlin\", \"nightly\", \"rand\", \"rand_core\", \"serde\", \"serde_bytes\", \"serde_crate\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 16409354033026609460, "profile": 8276155916380437441, "path": 17749628708327961150, "deps": [[2932480923465029663, "zeroize", false, 2078090615452242280], [4731167174326621189, "rand", false, 7981969016077068059], [9431183304631869056, "curve25519_dalek", false, 629710847643382688], [9689903380558560274, "serde_crate", false, 8602992869969445499], [11472355562936271783, "sha2", false, 12713374995283223976], [16629266738323756185, "ed25519", false, 10958620127410222979]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ed25519-dalek-9185ae788c92684f/dep-lib-ed25519_dalek", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}