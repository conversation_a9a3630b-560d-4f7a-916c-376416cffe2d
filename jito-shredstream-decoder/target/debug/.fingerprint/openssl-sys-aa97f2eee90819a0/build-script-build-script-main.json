{"rustc": 15497389221046826682, "features": "[\"openssl-src\", \"vendored\"]", "declared_features": "[\"aws-lc\", \"bindgen\", \"bssl-sys\", \"openssl-src\", \"unstable_boringssl\", \"vendored\"]", "target": 10419965325687163515, "profile": 3033921117576893, "path": 13271245703641790272, "deps": [[705192069995309102, "cc", false, 7797261203732423178], [3214373357989284387, "pkg_config", false, 9697515639132891920], [12933202132622624734, "vcpkg", false, 12950794470872204388], [15377254609210123429, "openssl_src", false, 16210912863660837118]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/openssl-sys-aa97f2eee90819a0/dep-build-script-build-script-main", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}