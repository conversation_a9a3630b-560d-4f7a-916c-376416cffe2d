{"rustc": 15497389221046826682, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 8276155916380437441, "path": 15033791335116528145, "deps": [[555019317135488525, "regex_automata", false, 11206086385466512188], [2779309023524819297, "aho_corasick", false, 17458365802258922496], [3129130049864710036, "memchr", false, 12015221177227729297], [9408802513701742484, "regex_syntax", false, 6222643329774453183]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-b5cd51b85bf35874/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}