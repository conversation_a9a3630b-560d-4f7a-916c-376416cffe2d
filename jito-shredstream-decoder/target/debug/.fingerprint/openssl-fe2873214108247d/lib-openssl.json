{"rustc": 15497389221046826682, "features": "[\"default\", \"vendored\"]", "declared_features": "[\"aws-lc\", \"bindgen\", \"default\", \"unstable_boringssl\", \"v101\", \"v102\", \"v110\", \"v111\", \"vendored\"]", "target": 17474193825155910204, "profile": 8276155916380437441, "path": 2246932632676733693, "deps": [[2924422107542798392, "libc", false, 7923484422187177304], [3722963349756955755, "once_cell", false, 10181385222418485424], [6635237767502169825, "foreign_types", false, 10088913479832572281], [7896293946984509699, "bitflags", false, 3760819982103396940], [8607891082156236373, "build_script_build", false, 17632426051382037742], [9070360545695802481, "ffi", false, 8671854762331471991], [10099563100786658307, "openssl_macros", false, 9839789785705827474], [10411997081178400487, "cfg_if", false, 10210376222278810431]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/openssl-fe2873214108247d/dep-lib-openssl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}