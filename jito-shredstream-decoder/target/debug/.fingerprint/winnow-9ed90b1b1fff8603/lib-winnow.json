{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 2173481344767076817, "path": 3622169010033162278, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/winnow-9ed90b1b1fff8603/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}